from io import <PERSON><PERSON><PERSON>
from unittest.mock import patch
from uuid import uuid4

from fastapi import status
from httpx import AsyncClient

from constants.message import UNDEFINED_REPLY, ConversationMessageIntention, MessageRole, MessageType
from constants.operation_ids import operation_ids


async def test_upload_documents_success(
    auth_mock,
    auth_header,
    async_client: AsyncClient,
    url_resolver,
    conversation_data: dict,
):
    user_message = 'Test message with files'

    mocked_intent = ConversationMessageIntention.UNDEFINED
    response_content = UNDEFINED_REPLY

    # First create a conversation
    conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    conversation_response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)
    conversation_id = conversation_response.json()['conversation']['id']

    # Use a supported file format (PDF)
    mime_type = 'application/pdf'

    # Prepare test files
    test_files = [
        ('files', ('test1.pdf', By<PERSON><PERSON>(b'Test file content 1'), mime_type)),
        ('files', ('test2.pdf', Bytes<PERSON>(b'Test file content 2'), mime_type)),
    ]

    # Prepare message data
    message_data = {
        'conversation_id': conversation_id,
        'content': user_message,
    }

    # Upload documents via message endpoint
    message_url = url_resolver.reverse(operation_ids.message.CREATE)
    with patch('services.conversation_message.ConversationMessageProcessor._get_intent', return_value=mocked_intent):
        response = await async_client.post(
            message_url,
            headers=auth_header,
            data=message_data,
            files=test_files,
        )

    # Verify response
    assert response.status_code == status.HTTP_201_CREATED
    data = response.json()

    # Verify message was created
    assert data['user']['conversation_id'] == conversation_id
    assert data['user']['content'] == user_message
    assert data['user']['role'] == str(MessageRole.USER)
    assert data['user']['type'] == str(MessageType.TEXT_WITH_FILE)
    assert data['system']['content'] == response_content

    # Verify files were created
    assert data['files'] is not None
    assert len(data['files']) == 2
    assert any(file['file_name'] == 'test1.pdf' for file in data['files'])
    assert any(file['file_name'] == 'test2.pdf' for file in data['files'])


async def test_upload_documents_nonexistent_conversation(
    auth_mock,
    auth_header,
    async_client: AsyncClient,
    url_resolver,
):
    # Generate a random UUID for a non-existent conversation
    nonexistent_id = str(uuid4())

    # Use a supported file format (PDF)
    mime_type = 'application/pdf'

    # Prepare test file
    test_files = [
        ('files', ('test.pdf', BytesIO(b'Test file content'), mime_type)),
    ]

    # Prepare message data with non-existent conversation
    message_data = {
        'conversation_id': nonexistent_id,
        'role': MessageRole.USER,
        'message_type': MessageType.TEXT,
        'content': 'Test message with files',
    }

    # Attempt to upload document to non-existent conversation
    message_url = url_resolver.reverse(operation_ids.message.CREATE)
    response = await async_client.post(
        message_url,
        headers=auth_header,
        data=message_data,
        files=test_files,
    )

    # Verify response
    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert 'detail' in response.json()


async def test_upload_documents_invalid_file_type(
    auth_mock,
    auth_header,
    async_client: AsyncClient,
    url_resolver,
    conversation_data: dict,
):
    # First create a conversation
    conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    conversation_response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)
    conversation_id = conversation_response.json()['conversation']['id']

    # Prepare message data
    message_data = {
        'conversation_id': conversation_id,
        'role': MessageRole.USER,
        'message_type': MessageType.TEXT,
        'content': 'Test message with invalid file type',
    }

    # Prepare test file with unsupported type
    test_files = [
        ('files', ('test.txt', BytesIO(b'Test file content'), 'text/plain')),
    ]

    # Attempt to upload document with invalid type
    message_url = url_resolver.reverse(operation_ids.message.CREATE)
    response = await async_client.post(
        message_url,
        headers=auth_header,
        data=message_data,
        files=test_files,
    )

    # Verify response
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert 'detail' in response.json()

    # Prepare test file with empty content type
    test_files = [
        ('files', ('test.txt', BytesIO(b'Test file content'), '')),
    ]

    # Attempt to upload document with invalid type
    response = await async_client.post(
        message_url,
        headers=auth_header,
        data=message_data,
        files=test_files,
    )

    # Verify response
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert 'detail' in response.json()


async def test_upload_documents_file_too_large(
    auth_mock,
    auth_header,
    async_client: AsyncClient,
    url_resolver,
    conversation_data: dict,
    monkeypatch,
):
    # Mock settings to restrict file size
    from config import settings

    monkeypatch.setattr(settings.document_storage, 'max_file_size', 10)  # 10 bytes

    # First create a conversation
    conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    conversation_response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)
    conversation_id = conversation_response.json()['conversation']['id']

    # Prepare message data
    message_data = {
        'conversation_id': conversation_id,
        'role': MessageRole.USER,
        'message_type': MessageType.TEXT,
        'content': 'Test message with file too large',
    }

    # Use a supported file format (PDF)
    mime_type = 'application/pdf'

    # Prepare test file that's too large
    test_files = [
        ('files', ('test.pdf', BytesIO(b'This content is more than 10 bytes'), mime_type)),
    ]

    # Attempt to upload document that's too large
    message_url = url_resolver.reverse(operation_ids.message.CREATE)
    response = await async_client.post(
        message_url,
        headers=auth_header,
        data=message_data,
        files=test_files,
    )

    # Verify response
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert 'detail' in response.json()


async def test_upload_documents_too_many_files(
    auth_mock,
    auth_header,
    async_client: AsyncClient,
    url_resolver,
    conversation_data: dict,
    monkeypatch,
):
    # Mock settings to restrict number of files
    from config import settings

    monkeypatch.setattr(settings.document_storage, 'max_docs_per_conversation', 1)

    # First create a conversation
    conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    conversation_response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)
    conversation_id = conversation_response.json()['conversation']['id']

    # Prepare message data
    message_data = {
        'conversation_id': conversation_id,
        'content': 'Test message with too many files',
    }

    # Use a supported file format (PDF)
    mime_type = 'application/pdf'

    # Prepare multiple test files
    test_files = [
        ('files', ('test1.pdf', BytesIO(b'Test file content 1'), mime_type)),
        ('files', ('test2.pdf', BytesIO(b'Test file content 2'), mime_type)),
    ]

    # Attempt to upload too many documents
    message_url = url_resolver.reverse(operation_ids.message.CREATE)
    response = await async_client.post(
        message_url,
        headers=auth_header,
        data=message_data,
        files=test_files,
    )

    # Verify response
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert 'detail' in response.json()


async def test_upload_documents_empty_filename(
    auth_mock,
    auth_header,
    async_client: AsyncClient,
    url_resolver,
    conversation_data: dict,
):
    # Create a conversation
    conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    conversation_response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)
    conversation_id = conversation_response.json()['conversation']['id']

    # Prepare message data
    message_data = {
        'conversation_id': conversation_id,
        'role': MessageRole.USER,
        'message_type': MessageType.TEXT,
        'content': 'Test message with empty filename',
    }

    # Prepare file with empty filename
    mime_type = 'application/pdf'
    test_files = [
        ('files', ('', BytesIO(b'Test file content'), mime_type)),
    ]

    # Attempt to upload document with empty filename
    message_url = url_resolver.reverse(operation_ids.message.CREATE)
    response = await async_client.post(
        message_url,
        headers=auth_header,
        data=message_data,
        files=test_files,
    )

    # Verify response
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    assert 'detail' in response.json()
    assert 'value_error' in str(response.json()['detail']).lower()

    # Prepare file with only extension
    test_files = [
        ('files', ('.pdf', BytesIO(b'Test file content'), mime_type)),
    ]

    # Attempt to upload document with only extension
    response = await async_client.post(
        message_url,
        headers=auth_header,
        data=message_data,
        files=test_files,
    )

    # Verify response
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert 'detail' in response.json()
    assert 'value_error' in str(response.json()['detail']).lower()


async def test_delete_conversation_removes_documents(
    auth_mock,
    auth_header,
    async_client: AsyncClient,
    url_resolver,
    conversation_data: dict,
):
    """Test that deleting a conversation also removes all associated documents."""
    conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    conversation_response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)
    conversation_id = conversation_response.json()['conversation']['id']

    mime_type = 'application/pdf'

    test_files = [
        ('files', ('test1.pdf', BytesIO(b'Test file content 1'), mime_type)),
    ]

    message_data = {
        'conversation_id': conversation_id,
        'content': 'Test message with files',
    }

    message_url = url_resolver.reverse(operation_ids.message.CREATE)

    new_conversation_data = conversation_data.copy()

    new_message_data = {
        'conversation_id': None,  # Will be filled after creating the second conversation
        'content': 'New message with same filename',
    }
    new_test_files = [
        ('files', ('test1.pdf', BytesIO(b'New file content'), mime_type)),
    ]

    message_response = await async_client.post(
        message_url,
        headers=auth_header,
        data=message_data,
        files=test_files,
    )

    data = message_response.json()
    message_id = data['user']['id']

    delete_url = url_resolver.reverse(operation_ids.conversation.DELETE, conversation_id=conversation_id)
    delete_response = await async_client.delete(delete_url, headers=auth_header)

    get_url = url_resolver.reverse(operation_ids.conversation.GET, conversation_id=conversation_id)
    get_response = await async_client.get(get_url, headers=auth_header)

    message_get_url = url_resolver.reverse(operation_ids.message.GET, message_id=message_id)
    message_get_response = await async_client.get(message_get_url, headers=auth_header)

    new_conversation_response = await async_client.post(
        conversation_url, headers=auth_header, json=new_conversation_data
    )
    new_conversation_id = new_conversation_response.json()['conversation']['id']

    new_message_data['conversation_id'] = new_conversation_id

    create_message_url = url_resolver.reverse(operation_ids.message.CREATE)
    new_message_response = await async_client.post(
        create_message_url,
        headers=auth_header,
        data=new_message_data,
        files=new_test_files,
    )

    assert message_response.status_code == status.HTTP_201_CREATED
    assert data['files'] is not None
    assert len(data['files']) == 1

    assert delete_response.status_code == status.HTTP_204_NO_CONTENT
    assert get_response.status_code == status.HTTP_404_NOT_FOUND
    assert message_get_response.status_code == status.HTTP_404_NOT_FOUND
    assert new_message_response.status_code == status.HTTP_201_CREATED

    new_data = new_message_response.json()
    assert new_data['files'] is not None
    assert len(new_data['files']) == 1
    assert new_data['files'][0]['file_name'] == 'test1.pdf'
